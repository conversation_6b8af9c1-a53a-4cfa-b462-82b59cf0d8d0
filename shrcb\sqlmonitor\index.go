package sqlmonitor

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

func (s *SQLMonitor) StartMonitor() {
	engine := gin.Default()

	// 跨域中间件
	engine.Use(Cors())

	engine.GET("/api/v1/task/exec", s.CreateTaskPlanDemo)
	engine.POST("/api/v1/task/exec", s.CreateTaskPlan)
	engine.POST("/api/v1/task/exec/update", s.UpdateTaskPlan)
	engine.POST("/api/v1/task/exec/select", s.QueryTaskPlan)
	engine.POST("/api/v1/task/exec/delete/id", s.DeleteTaskPlan)

	engine.GET("/api/v1/task/group", s.CreateTaskGroupDemo)
	engine.POST("/api/v1/task/group/add", s.CreateTaskGroup)
	engine.POST("/api/v1/task/group/select", s.QueryTaskGroup)
	engine.POST("/api/v1/task/group/select/all", s.QueryTaskGroupAll)
	engine.POST("/api/v1/task/group/update", s.UpdateTaskGroup)
	engine.POST("/api/v1/task/group/delete/id", s.DeleteTaskGroup)

	s.DB = GetOrmMySQL()

	_ = s.DB.AutoMigrate(&TaskPlan{})
	_ = s.DB.AutoMigrate(&TaskGroup{})

	_ = engine.Run(":1010")

}

func (s *SQLMonitor) CreateTaskPlanDemo(ctx *gin.Context) {

	// 模拟数据量
	demoNum := 50

	var demoDataList []TaskPlan

	groups := []string{"1", "2", "3", "4", "5", "6", "7", "8", "9", "10"}
	frequencyValues := []string{"10", "20", "30", "40", "50"}
	frequencyUnits := []string{"sec", "min", "hour", "day"}
	statusValues := []string{"运行中", "已停止", "等待中", "失败", "成功"}
	weekdayValues := []string{"1", "2", "3", "4", "5", "6", "7"}
	retryNumValues := []string{"0", "1", "2", "3", "5"}
	retryFrequencyValues := []string{"5", "10", "15", "30", "60"}
	IdList := []string{"1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20"}

	// 任务名称模板
	taskNames := []string{
		"数据库备份任务",
		"日志清理任务",
		"系统监控任务",
		"性能检测任务",
		"安全扫描任务",
		"文件同步任务",
		"缓存清理任务",
		"报表生成任务",
		"数据迁移任务",
		"健康检查任务",
	}

	// 生成模拟数据
	for i := 0; i < demoNum; i++ {
		task := TaskPlan{
			Name:           fmt.Sprintf("%s_%d", taskNames[i%len(taskNames)], i+1),
			GroupId:        groups[i%len(groups)],
			Status:         statusValues[i%len(statusValues)],
			StartTime:      fmt.Sprintf("%02d:00:00", (i % 24)),
			EndTime:        fmt.Sprintf("%02d:30:00", (i % 24)),
			Weekday:        weekdayValues[i%len(weekdayValues)],
			Frequency:      frequencyValues[i%len(frequencyValues)] + frequencyUnits[i%len(frequencyUnits)],
			RetryNum:       retryNumValues[i%len(retryNumValues)],
			RetryFrequency: retryFrequencyValues[i%len(retryFrequencyValues)],
			AlertTaskId:    IdList[i%len(IdList)],
			AlertSendId:    IdList[i%len(IdList)],
			DBConnectionId: IdList[i%len(IdList)],
			OtherInfoId:    IdList[i%len(IdList)],
		}
		demoDataList = append(demoDataList, task)
	}

	// 批量插入数据
	result := s.DB.CreateInBatches(&demoDataList, 100) // 每批插入100条
	if result.Error != nil {
		var response Response
		response.Success = false
		response.Message = fmt.Sprintf("批量插入失败: %v", result.Error)
		response.Total = 0
		ctx.JSON(500, response)
		return
	}

	var response Response
	response.Success = true
	response.Message = fmt.Sprintf("成功创建 %d 条模拟数据", result.RowsAffected)
	response.Total = result.RowsAffected
	response.Data = fmt.Sprintf("已插入 %d 条记录", result.RowsAffected)

	ctx.JSON(200, response)

}

func (s *SQLMonitor) CreateTaskGroupDemo(ctx *gin.Context) {
	// 模拟数据量
	demoNum := 1000

	var demoDataList []TaskGroup

	// 任务分组名称模板
	groupNames := []string{
		"数据同步",
		"报表生成",
		"数据清理",
		"系统监控",
		"备份任务",
		"日志分析",
		"性能优化",
		"安全检查",
		"数据迁移",
		"接口测试",
		"用户管理",
		"权限控制",
		"消息推送",
		"文件处理",
		"图片压缩",
		"视频转码",
		"邮件发送",
		"短信通知",
		"支付处理",
		"订单同步",
		"定时任务",
		"数据导入",
		"数据导出",
		"系统维护",
		"缓存管理",
		"队列处理",
		"文件上传",
		"文件下载",
		"API调用",
		"数据校验",
	}

	// 是否被引用的状态
	isUsedValues := []bool{true, false}

	// 生成模拟数据
	for i := 0; i < demoNum; i++ {
		group := TaskGroup{
			Name:   fmt.Sprintf("%s_%d", groupNames[i%len(groupNames)], i+1),
			IsUsed: isUsedValues[i%len(isUsedValues)],
		}
		demoDataList = append(demoDataList, group)
	}

	// 批量插入数据
	result := s.DB.CreateInBatches(&demoDataList, 100) // 每批插入100条
	if result.Error != nil {
		var response Response
		response.Success = false
		response.Message = fmt.Sprintf("批量插入失败: %v", result.Error)
		response.Total = 0
		ctx.JSON(500, response)
		return
	}

	var response Response
	response.Success = true
	response.Message = fmt.Sprintf("成功创建 %d 条任务分组模拟数据", result.RowsAffected)
	response.Total = result.RowsAffected
	response.Data = fmt.Sprintf("已插入 %d 条记录", result.RowsAffected)

	ctx.JSON(200, response)
}

func (s *SQLMonitor) CreateTaskGroup(ctx *gin.Context) {

	var params TaskGroup
	err := ctx.ShouldBind(&params)
	if err != nil {
		fmt.Println(err)
	}

	s.DB.Debug().Omit("id", "create_time", "update_time").Create(&params)

	var response Response

	response.Success = true
	response.Message = "创建成功"
	response.Total = 100

	ctx.JSON(200, response)
}

func (s *SQLMonitor) CreateTaskPlan(ctx *gin.Context) {

	var params TaskPlanWithGroupName
	err := ctx.ShouldBind(&params)
	if err != nil {
		fmt.Println(err)
	}

	if params.GroupId == "" {
		var taskGroup TaskGroup
		s.DB.Where("name = ?", params.GroupName).First(&taskGroup)
		params.GroupId = strconv.Itoa(int(taskGroup.Id))
	}

	s.DB.Debug().Omit("id", "create_time", "update_time", "group_name").Create(&params)

	var response Response

	response.Success = true
	response.Message = "创建成功"
	response.Total = 100

	ctx.JSON(200, response)

}

func (s *SQLMonitor) UpdateTaskGroup(ctx *gin.Context) {

	var params TaskGroup
	_ = ctx.ShouldBind(&params)

	fmt.Println(params)

	res := s.DB.Debug().Omit("id", "create_time", "update_time").
		Model(TaskGroup{}).Where("id = ?", params.Id).Updates(&params)

	var response Response

	if res.RowsAffected == 1 {
		response.Success = true
		response.Message = "更新成功"
	} else {
		response.Success = false
		response.Message = "更新失败" + res.Error.Error()
	}

	//response.Total = 100
	//response.Data = []TaskPlan{}

	ctx.JSON(200, response)

}

func (s *SQLMonitor) UpdateTaskPlan(ctx *gin.Context) {

	var params TaskPlanWithGroupName
	_ = ctx.ShouldBind(&params)

	if params.GroupId == "" {
		var taskGroup TaskGroup
		s.DB.Where("name = ?", params.GroupName).First(&taskGroup)

		params.GroupId = strconv.Itoa(int(taskGroup.Id))
	}

	s.DB.Model(TaskPlan{}).Omit("task_name").Where("id = ?", params.Id).Updates(&params)

	var response Response

	response.Success = true
	response.Message = "创建成功"
	response.Total = 100
	//response.Data = []TaskPlan{}

	ctx.JSON(200, response)

}

func (s *SQLMonitor) DeleteTaskGroup(ctx *gin.Context) {

	// 前端请求数据
	type DeleteId struct {
		IdList []int `json:"ids"`
	}

	var params DeleteId
	err := ctx.ShouldBind(&params)
	if err != nil {
		fmt.Println(err)
	}

	res := s.DB.Debug().Where("id in ?", params.IdList).Delete(&TaskGroup{})

	var response Response

	response.Success = true
	response.Message = "删除成功"
	response.Total = res.RowsAffected
	// response.Data = deleteStatusList

	ctx.JSON(200, response)

}

func (s *SQLMonitor) DeleteTaskPlan(ctx *gin.Context) {

	// 前端请求数据
	type DeleteId struct {
		IdList []int `json:"ids"`
	}

	var params DeleteId
	_ = ctx.ShouldBind(&params)

	res := s.DB.Where("id in ?", params.IdList).Delete(&TaskPlan{})

	var response Response

	response.Success = true
	response.Message = "删除成功"
	response.Total = res.RowsAffected
	// response.Data = deleteStatusList

	ctx.JSON(200, response)

}

func (s *SQLMonitor) QueryTaskGroupAll(ctx *gin.Context) {

	var res []TaskGroup

	// 查询所有
	s.DB.Debug().Find(&res)

	// 统计总记录数
	var total int64
	s.DB.Model(&TaskGroup{}).Count(&total)

	var response Response

	response.Success = true
	response.Message = "查询成功"
	response.Total = total
	response.Data = res

	ctx.JSON(200, response)

}

func (s *SQLMonitor) QueryTaskGroup(ctx *gin.Context) {

	type SearchParams struct {
		BasicSearchParams
		Name   string `json:"name"`
		IsUsed bool   `json:"isUsed"`
	}

	var params SearchParams
	_ = ctx.ShouldBind(&params)

	// 设置默认分页参数
	if params.Current <= 0 {
		params.Current = 1
	}

	if params.PageSize <= 0 {
		params.PageSize = 10
	}

	// 计算偏移量
	offset := (params.Current - 1) * params.PageSize

	var res []TaskGroup

	s.DB.Debug().Limit(params.PageSize).Offset(offset).Find(&res)
	//fmt.Println(res)

	// 统计总记录数
	var total int64
	s.DB.Model(&TaskGroup{}).Count(&total)

	var response Response

	response.Success = true
	response.Message = "查询成功"
	response.Total = total
	response.Data = res

	ctx.JSON(200, response)

}

func (s *SQLMonitor) QueryTaskPlan(ctx *gin.Context) {

	var params BasicSearchParams
	_ = ctx.ShouldBind(&params)

	// 设置默认分页参数
	if params.Current <= 0 {
		params.Current = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 10
	}

	// 计算偏移量
	offset := (params.Current - 1) * params.PageSize

	var res []TaskPlanWithGroupName

	// s.DB.Debug().Limit(params.PageSize).Offset(offset).Find(&res)

	s.DB.Debug().Raw(`
		SELECT
			tp.*,
			tg.name as group_name
		FROM
			task_plan tp
		LEFT JOIN (
			SELECT
				id,
				name
			FROM
				task_group) tg
		ON
			tg.id = tp.group_id
		LIMIT ? OFFSET ?
	`, params.PageSize, offset).Scan(&res)

	fmt.Println(res, "query res")

	// 统计总记录数
	var total int64
	s.DB.Model(&TaskPlan{}).Count(&total)

	var response Response

	//bty, _ := json.Marshal(&res)

	response.Success = true
	response.Message = "查询成功"
	response.Total = total
	response.Data = res

	ctx.JSON(200, response)

}

// Cors 处理跨域请求,支持options访问
func Cors() gin.HandlerFunc {
	return func(c *gin.Context) {
		method := c.Request.Method
		origin := c.Request.Header.Get("Origin")
		if origin != "" {
			// 允许所有来源
			c.Header("Access-Control-Allow-Origin", "*")
			// 允许的请求方法
			c.Header("Access-Control-Allow-Methods", "POST, GET, OPTIONS, PUT, DELETE, UPDATE")
			// 允许的请求头
			c.Header("Access-Control-Allow-Headers", "Origin, X-Requested-With, Content-Type, Accept, Authorization")
			// 允许暴露的响应头
			c.Header("Access-Control-Expose-Headers", "Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Cache-Control, Content-Language, Content-Type")
			// 是否允许携带cookie
			c.Header("Access-Control-Allow-Credentials", "true")
		}

		// 放行所有OPTIONS方法
		if method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
		}

		c.Next()
	}
}
